{"buildFiles": ["/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake", "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake", "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake", "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake", "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"expo-modules-core::@6890427a1f51a3e7e1df": {"artifactName": "expo-modules-core", "abi": "arm64-v8a", "output": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/build/intermediates/cxx/Debug/xc4a2z11/obj/arm64-v8a/libexpo-modules-core.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so", "/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"]}}}