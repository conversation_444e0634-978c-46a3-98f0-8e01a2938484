# ninja log v5
1	1186	1759823975250022498	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o	65c8ebab33d7ea36
1	1222	1759823975286119425	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o	85b7af205813bd5f
1	1236	1759823975296872504	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o	17aa64c35ca9e7c8
1	1239	1759823975286707631	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o	7e367a9e726b98f
2	1359	1759823975422277170	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o	7fcbeb4176cf6cd5
2	1430	1759823975487967136	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o	aeb9d5a0ffc8d96a
1	1558	1759823975621302975	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o	f11037c80135f1ef
1	1986	1759823976043225317	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o	b802fe9924cea266
2	2348	1759823976403174511	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o	b34315927fbb5b7b
1359	2369	1759823976432872591	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o	3d56a32bce562412
1558	2873	1759823976929558525	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o	e20bb5acb7adfe0c
2	2963	1759823977019549011	CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o	e0314179e1db642b
0	2996	1759823977046825393	CMakeFiles/expo-modules-core.dir/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o	54a89ad0fb2387a9
1	3346	1759823977406622504	CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o	5ddb27cb1f5f677a
1189	3770	1759823977826042856	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o	ddbd5350311e02ed
1239	3893	1759823977954851882	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o	1ec2a083539009d0
2349	4854	1759823978912548282	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o	8b92a7e8a1c0f687
1236	5165	1759823979216434510	CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o	749af3712860e040
1986	5190	1759823979250290114	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o	ff46b665833f11f1
1223	5283	1759823979339297229	CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o	aa4fb44387c56db2
3894	5393	1759823979455803475	CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o	15bb48b44d071f95
2873	5699	1759823979743900895	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o	ef76ce15df2d7031
2964	5856	1759823979915216112	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o	c83919e403a1d28a
2996	5994	1759823980054744217	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o	2f09e2aa6b860d63
2369	6004	1759823980062108519	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o	722cbee8acdc7f9f
3346	6062	1759823980124168209	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o	94d3aff11dc6e5d1
5190	6277	1759823980339221321	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o	330bb104bba99913
4854	7023	1759823981078938955	CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o	58748e4c20af4045
3770	7163	1759823981200528096	CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o	5f886253d68caac0
5165	7194	1759823981254465696	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o	d8d34cc34aac28e6
5394	7443	1759823981502925412	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o	a0fa19eb69dea2e0
1431	8043	1759823982081680451	CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o	2527c968bf6e7f8
6062	8086	1759823982145306176	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o	4d0b860218320641
6277	8204	1759823982265361574	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o	2296a7e1e9a89afd
5856	8221	1759823982280830173	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o	963bf231671fbc24
5283	8230	1759823982290471965	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o	6a90a1443a975b69
6004	8267	1759823982327424680	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o	44ba3367d74c6da
5699	8432	1759823982491050889	CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o	6ebfca9a27a009a6
5994	8437	1759823982497243154	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o	5c9bf0c836381902
7023	8627	1759823982690185944	CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o	db8e49d141dd574a
8627	8738	1759823982783767872	../../../../build/intermediates/cxx/Debug/xc4a2z11/obj/arm64-v8a/libexpo-modules-core.so	5b0058acad7b6e84
