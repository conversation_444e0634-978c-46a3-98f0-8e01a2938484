{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "expo-modules-core", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "expo-modules-core::@6890427a1f51a3e7e1df", "jsonFile": "target-expo-modules-core-Debug-eb9a9a8a823a6fdfaab7.json", "name": "expo-modules-core", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/arm64-v8a", "source": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android"}, "version": {"major": 2, "minor": 3}}