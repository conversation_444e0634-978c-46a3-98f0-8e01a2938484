[{"level_": 0, "message_": "Start JSON generation. Platform version: 31 min SDK version: arm64-v8a", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON /Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/arm64-v8a/android_gradle_build.json due to:", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/java \\\n  --class-path \\\n  /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.prefab/cli/2.1.0/aa32fec809c44fa531f01dcfb739b5b3304d3050/cli-2.1.0-all.jar \\\n  com.google.prefab.cli.AppKt \\\n  --build-system \\\n  cmake \\\n  --platform \\\n  android \\\n  --abi \\\n  arm64-v8a \\\n  --os-version \\\n  31 \\\n  --stl \\\n  c++_shared \\\n  --ndk-version \\\n  27 \\\n  --output \\\n  /var/folders/sr/gq427xp54sv4__50jx397hlw0000gn/T/agp-prefab-staging13443484603368025076/staged-cli-output \\\n  /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab \\\n  /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab\n", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from '/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/arm64-v8a'", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder '/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/arm64-v8a'", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=31 \\\n  -DANDROID_PLATFORM=android-31 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/build/intermediates/cxx/Debug/xc4a2z11/obj/arm64-v8a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/build/intermediates/cxx/Debug/xc4a2z11/obj/arm64-v8a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/prefab/arm64-v8a/prefab \\\n  -B/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/arm64-v8a \\\n  -GNinja \\\n  -DANDROID_STL=c++_shared \\\n  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON \\\n  -DREACT_NATIVE_DIR=/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/react-native \\\n  -DREACT_NATIVE_TARGET_VERSION=79 \\\n  -DUSE_HERMES=false \\\n  -DIS_NEW_ARCHITECTURE_ENABLED=false \\\n  -DUNIT_TEST=false\n", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \\\n  -H/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android \\\n  -DCMAKE_SYSTEM_NAME=Android \\\n  -DCMA<PERSON>_EXPORT_COMPILE_COMMANDS=ON \\\n  -DCMAKE_SYSTEM_VERSION=31 \\\n  -DANDROID_PLATFORM=android-31 \\\n  -DANDROID_ABI=arm64-v8a \\\n  -DCMAKE_ANDROID_ARCH_ABI=arm64-v8a \\\n  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \\\n  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake \\\n  -DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \\\n  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/build/intermediates/cxx/Debug/xc4a2z11/obj/arm64-v8a \\\n  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/build/intermediates/cxx/Debug/xc4a2z11/obj/arm64-v8a \\\n  -DCMAKE_BUILD_TYPE=Debug \\\n  -DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/prefab/arm64-v8a/prefab \\\n  -B/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/arm64-v8a \\\n  -GNinja \\\n  -DANDROID_STL=c++_shared \\\n  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON \\\n  -DREACT_NATIVE_DIR=/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/react-native \\\n  -DREACT_NATIVE_TARGET_VERSION=79 \\\n  -DUSE_HERMES=false \\\n  -DIS_NEW_ARCHITECTURE_ENABLED=false \\\n  -DUNIT_TEST=false\n", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of /Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/arm64-v8a/compile_commands.json.bin normally", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked /Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/Debug/xc4a2z11/arm64-v8a/compile_commands.json to /Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/.cxx/tools/debug/arm64-v8a/compile_commands.json", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/Documents/i-Jobs/dhamma-edit/app/node_modules/expo-modules-core/android/CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]