/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home/bin/java \
  --class-path \
  /Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.prefab/cli/2.1.0/aa32fec809c44fa531f01dcfb739b5b3304d3050/cli-2.1.0-all.jar \
  com.google.prefab.cli.AppKt \
  --build-system \
  cmake \
  --platform \
  android \
  --abi \
  arm64-v8a \
  --os-version \
  31 \
  --stl \
  c++_shared \
  --ndk-version \
  27 \
  --output \
  /var/folders/sr/gq427xp54sv4__50jx397hlw0000gn/T/agp-prefab-staging13443484603368025076/staged-cli-output \
  /Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab \
  /Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab
