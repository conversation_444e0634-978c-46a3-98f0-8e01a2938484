<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColor">@android:color/black</item>
        <item name="android:editTextStyle">@style/ResetEditText</item>
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="android:statusBarColor">#DDD</item>
        <item name="android:forceDarkAllowed">false</item>
    </style>
    <style name="ResetEditText" parent="@android:style/Widget.EditText">
        <item name="android:padding">0dp</item>
        <item name="android:textColorHint">#c8c8c8</item>
        <item name="android:textColor">@android:color/black</item>
    </style>
</resources>